<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoGeneratedRunConfigurationManager">
    <projectFile>Hl7Validator.csproj</projectFile>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="fbd8469e-12f1-46f6-a6fc-1fe8821ff68a" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/Program.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Program.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/TestMessages/SubFolder/sample_v29.hl7" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/TestMessages/sample_v25.hl7" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/TestMessages/sample_v28.hl7" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="DpaMonitoringSettings">
    <option name="firstShow" value="false" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitHubPullRequestSearchHistory"><![CDATA[{
  "lastFilter": {
    "state": "OPEN",
    "assignee": "nirzaf"
  }
}]]></component>
  <component name="GithubPullRequestsUISettings"><![CDATA[{
  "selectedUrlAndAccountId": {
    "url": "https://github.com/nirzaf/Hl7Validator.git",
    "accountId": "7fcca842-ed42-4dff-9677-02e4bb6f80c6"
  }
}]]></component>
  <component name="MetaFilesCheckinStateConfiguration" checkMetaFiles="true" />
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2zUL0p034hfCFW46RwUCTePk7EY" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    ".NET Project.Hl7Validator.executor": "Debug",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "master",
    "ignore.virus.scanning.warn.message": "true",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager">
    <configuration name="Hl7Validator" type="DotNetProject" factoryName=".NET Project">
      <option name="EXE_PATH" value="" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="ENV_FILE_PATHS" value="" />
      <option name="REDIRECT_INPUT_PATH" value="" />
      <option name="PTY_MODE" value="Auto" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="AUTO_ATTACH_CHILDREN" value="0" />
      <option name="MIXED_MODE_DEBUG" value="0" />
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/Hl7Validator.csproj" />
      <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
      <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
      <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
      <option name="PROJECT_KIND" value="DotNetCore" />
      <option name="PROJECT_TFM" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="fbd8469e-12f1-46f6-a6fc-1fe8821ff68a" name="Changes" comment="" />
      <created>1751780505098</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751780505098</updated>
      <workItem from="1751780539266" duration="617000" />
      <workItem from="1751782768430" duration="4005000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnityCheckinConfiguration" checkUnsavedScenes="true" />
  <component name="UnityProjectConfiguration" hasMinimizedUI="false" />
  <component name="VcsManagerConfiguration">
    <option name="CLEAR_INITIAL_COMMIT_MESSAGE" value="true" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.OperationCanceledException" breakIfHandledByOtherCode="false" displayValue="System.OperationCanceledException" />
          <option name="timeStamp" value="1" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.Tasks.TaskCanceledException" breakIfHandledByOtherCode="false" displayValue="System.Threading.Tasks.TaskCanceledException" />
          <option name="timeStamp" value="2" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.ThreadAbortException" breakIfHandledByOtherCode="false" displayValue="System.Threading.ThreadAbortException" />
          <option name="timeStamp" value="3" />
        </breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>