using NHapi.Base.Parser;
using NHapi.Base.Model;
using NHapi.Base.Util;
using NHapi.Base.Validation;
using System.Text;
using System.Diagnostics;

namespace Hl7Validator;

/// <summary>
/// HL7 Message Validator v2.5+
///
/// Features:
/// - Validates HL7 messages against v2.5+ standards
/// - Automatically fixes common formatting issues (missing 'M' in MSH)
/// - Lenient validation for legacy data types (CE vs CWE)
/// - Comprehensive validation reporting with insights
/// - Command-line limit support for large datasets
/// - Performance metrics and file statistics
/// </summary>
class Program
{
    // Hardcoded folder path - modify this path as needed
    private const string Hl7FolderPath = @"TestMessages";

    // Supported HL7 file extensions
    private static readonly string[] Hl7Extensions = [".hl7", ".txt", ".msg"];

    static async Task Main(string[] args)
    {
        var stopwatch = Stopwatch.StartNew();

        // Parse command line arguments
        var config = ParseCommandLineArguments(args);

        Console.WriteLine("HL7 Message Validator v2.5+");
        Console.WriteLine("============================");
        Console.WriteLine($"Scanning folder: {Hl7FolderPath}");
        if (config.MessageLimit.HasValue)
        {
            Console.WriteLine($"Message limit: {config.MessageLimit.Value}");
        }
        else
        {
            Console.WriteLine("Message limit: No limit (processing all files)");
        }
        Console.WriteLine();

        try
        {
            if (!Directory.Exists(Hl7FolderPath))
            {
                Console.WriteLine($"Error: Folder '{Hl7FolderPath}' does not exist.");
                Console.WriteLine("Please update the HL7_FOLDER_PATH constant in the code to point to your HL7 messages folder.");
                return;
            }

            var allHl7Files = GetHl7Files(Hl7FolderPath);

            if (!allHl7Files.Any())
            {
                Console.WriteLine($"No HL7 files found in '{Hl7FolderPath}' or its subfolders.");
                Console.WriteLine($"Looking for files with extensions: {string.Join(", ", Hl7Extensions)}");
                return;
            }

            // Apply limit if specified
            var hl7FilesToProcess = config.MessageLimit.HasValue
                ? allHl7Files.Take(config.MessageLimit.Value).ToList()
                : allHl7Files;

            Console.WriteLine($"Found {allHl7Files.Count} HL7 file(s) total");
            if (config.MessageLimit.HasValue && allHl7Files.Count > config.MessageLimit.Value)
            {
                Console.WriteLine($"Processing first {hl7FilesToProcess.Count} files due to limit");
            }
            else
            {
                Console.WriteLine($"Processing all {hl7FilesToProcess.Count} files");
            }
            Console.WriteLine();

            var validationResults = new List<ValidationResult>();
            var processedCount = 0;

            foreach (var filePath in hl7FilesToProcess)
            {
                processedCount++;
                Console.WriteLine($"[{processedCount}/{hl7FilesToProcess.Count}] Processing: {Path.GetRelativePath(Hl7FolderPath, filePath)}");
                var result = await ValidateHl7File(filePath);
                validationResults.Add(result);

                Console.WriteLine($"  Status: {(result.IsValid ? "✓ VALID" : "✗ INVALID")}");
                if (!result.IsValid)
                {
                    Console.WriteLine($"  Error: {result.ErrorMessage}");
                }
                if (!string.IsNullOrEmpty(result.Version))
                {
                    Console.WriteLine($"  Version: {result.Version}");
                }
                else if (result.IsValid)
                {
                    Console.WriteLine($"  Version: Could not extract version");
                }
                if (result.FileSizeBytes > 0)
                {
                    Console.WriteLine($"  Size: {FormatFileSize(result.FileSizeBytes)}");
                }
                Console.WriteLine();
            }

            stopwatch.Stop();

            // Generate comprehensive insights report
            await GenerateInsightsReport(validationResults, allHl7Files.Count, config, stopwatch.Elapsed);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"An error occurred: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }

        Console.WriteLine();
        Console.WriteLine("Press any key to exit...");
        Console.ReadKey();
    }

    /// <summary>
    /// Recursively gets all HL7 files from the specified directory and its subdirectories
    /// </summary>
    private static List<string> GetHl7Files(string directoryPath)
    {
        var hl7Files = new List<string>();

        try
        {
            // Get files in current directory
            foreach (var extension in Hl7Extensions)
            {
                var files = Directory.GetFiles(directoryPath, $"*{extension}", SearchOption.TopDirectoryOnly);
                hl7Files.AddRange(files);
            }

            // Recursively search subdirectories
            var subdirectories = Directory.GetDirectories(directoryPath);
            foreach (var subdirectory in subdirectories)
            {
                hl7Files.AddRange(GetHl7Files(subdirectory));
            }
        }
        catch (UnauthorizedAccessException ex)
        {
            Console.WriteLine($"Warning: Access denied to directory '{directoryPath}': {ex.Message}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Warning: Error accessing directory '{directoryPath}': {ex.Message}");
        }

        return hl7Files;
    }

    /// <summary>
    /// Validates an HL7 file and checks if it meets v2.5+ standards
    /// </summary>
    private static async Task<ValidationResult> ValidateHl7File(string filePath)
    {
        var stopwatch = Stopwatch.StartNew();
        long fileSize = 0;
        string content = string.Empty; // Declare at broader scope

        try
        {
            // Get file size
            var fileInfo = new FileInfo(filePath);
            fileSize = fileInfo.Length;

            // Read the file content
            content = await File.ReadAllTextAsync(filePath, Encoding.UTF8);

            if (string.IsNullOrWhiteSpace(content))
            {
                stopwatch.Stop();
                return new ValidationResult
                {
                    FilePath = filePath,
                    IsValid = false,
                    ErrorMessage = "File is empty or contains only whitespace",
                    FileSizeBytes = fileSize,
                    ProcessingTime = stopwatch.Elapsed
                };
            }

            // Clean up the content - remove any BOM, normalize line endings
            content = content.Trim();
            if (content.StartsWith("\uFEFF"))
            {
                content = content.Substring(1); // Remove BOM
            }

            // Fix common issue: Missing "M" at the beginning of MSH segment
            if (content.StartsWith("SH|"))
            {
                content = "M" + content;
            }

            // Replace different line ending types with standard HL7 segment separator
            content = content.Replace("\r\n", "\r").Replace("\n", "\r");

            // Optional: Show preprocessing info (comment out for production)
            // var debugContent = content.Length > 100 ? content.Substring(0, 100) : content;
            // Console.WriteLine($"    Debug: Content starts with: {debugContent.Replace("\r", "\\r")}");
            // if (wasFixed)
            // {
            //     Console.WriteLine($"    Debug: Fixed missing 'M' in MSH segment");
            // }

            // Create parser - NHapi will perform basic validation during parsing
            var parser = new PipeParser();

            // Parse the message - this will now throw ValidationException for HL7 rule violations
            IMessage message = parser.Parse(content);

            if (message == null)
            {
                stopwatch.Stop();
                return new ValidationResult
                {
                    FilePath = filePath,
                    IsValid = false,
                    ErrorMessage = "Failed to parse HL7 message - message is null",
                    FileSizeBytes = fileSize,
                    ProcessingTime = stopwatch.Elapsed
                };
            }

            // Get version from MSH segment
            string version = GetHl7Version(message);

            // Validate version is 2.8 or above
            bool isValidVersion = IsVersion28OrAbove(version);

            if (!isValidVersion)
            {
                stopwatch.Stop();
                return new ValidationResult
                {
                    FilePath = filePath,
                    IsValid = false,
                    ErrorMessage = $"HL7 version '{version}' is not 2.5 or above",
                    Version = version,
                    FileSizeBytes = fileSize,
                    ProcessingTime = stopwatch.Elapsed
                };
            }

            // Additional validation - check if message has required segments
            bool hasRequiredSegments = ValidateRequiredSegments(message);

            if (!hasRequiredSegments)
            {
                stopwatch.Stop();
                return new ValidationResult
                {
                    FilePath = filePath,
                    IsValid = false,
                    ErrorMessage = "Message is missing required segments (MSH is mandatory)",
                    Version = version,
                    FileSizeBytes = fileSize,
                    ProcessingTime = stopwatch.Elapsed
                };
            }

            stopwatch.Stop();
            return new ValidationResult
            {
                FilePath = filePath,
                IsValid = true,
                Version = version,
                FileSizeBytes = fileSize,
                ProcessingTime = stopwatch.Elapsed
            };
        }
        catch (ValidationException valEx)
        {
            // Handle data type validation issues more leniently
            // If it's just a data type issue (like CE vs CWE), treat as valid but with warning
            if (valEx.Message.Contains("is invalid for version") &&
                (valEx.Message.Contains("CE") || valEx.Message.Contains("data type")))
            {
                stopwatch.Stop();
                // Get version anyway since the message parsed successfully
                string version = "Unknown";
                try
                {
                    // Try to extract version even though validation failed
                    // Use a simple regex to extract version from MSH-12 field
                    var mshMatch = System.Text.RegularExpressions.Regex.Match(content, @"MSH\|[^|]*\|[^|]*\|[^|]*\|[^|]*\|[^|]*\|[^|]*\|[^|]*\|[^|]*\|[^|]*\|[^|]*\|([^|]*)\|");
                    if (mshMatch.Success && mshMatch.Groups.Count > 1)
                    {
                        version = mshMatch.Groups[1].Value.Trim();
                    }
                }
                catch { }

                return new ValidationResult
                {
                    FilePath = filePath,
                    IsValid = true, // Treat as valid despite data type warnings
                    ErrorMessage = $"Warning: {valEx.Message.ReplaceLineEndings(" ")}",
                    Version = version,
                    FileSizeBytes = fileSize,
                    ProcessingTime = stopwatch.Elapsed
                };
            }

            // For other validation errors, still treat as invalid
            stopwatch.Stop();
            return new ValidationResult
            {
                FilePath = filePath,
                IsValid = false,
                ErrorMessage = $"HL7 Conformance Error: {valEx.Message.ReplaceLineEndings(" ")}",
                FileSizeBytes = fileSize,
                ProcessingTime = stopwatch.Elapsed
            };
        }
        catch (Exception ex)
        {
            // Check if this is a data type validation error that we should treat leniently
            if (ex.Message.Contains("is invalid for version") &&
                (ex.Message.Contains("CE") || ex.Message.Contains("data type")))
            {
                stopwatch.Stop();
                // Get version anyway since the message parsed successfully
                string version = "Unknown";
                try
                {
                    // Try to extract version even though validation failed
                    // Use a simple regex to extract version from MSH-12 field
                    var mshMatch = System.Text.RegularExpressions.Regex.Match(content, @"MSH\|[^|]*\|[^|]*\|[^|]*\|[^|]*\|[^|]*\|[^|]*\|[^|]*\|[^|]*\|[^|]*\|[^|]*\|([^|]*)\|");
                    if (mshMatch.Success && mshMatch.Groups.Count > 1)
                    {
                        version = mshMatch.Groups[1].Value.Trim();
                    }
                }
                catch { }

                return new ValidationResult
                {
                    FilePath = filePath,
                    IsValid = true, // Treat as valid despite data type warnings
                    ErrorMessage = $"Warning: {ex.Message.ReplaceLineEndings(" ")}",
                    Version = version,
                    FileSizeBytes = fileSize,
                    ProcessingTime = stopwatch.Elapsed
                };
            }

            // Catches other errors like general parsing failures
            stopwatch.Stop();
            return new ValidationResult
            {
                FilePath = filePath,
                IsValid = false,
                ErrorMessage = $"Validation error: {ex.Message}",
                FileSizeBytes = fileSize,
                ProcessingTime = stopwatch.Elapsed
            };
        }
    }

    /// <summary>
    /// Extracts the HL7 version from the message using the Terser (recommended NHapi practice)
    /// </summary>
    private static string GetHl7Version(IMessage message)
    {
        if (message == null) return "Unknown";

        try
        {
            var terser = new Terser(message);
            // MSH-12 is the Version ID field in HL7 messages
            string version = terser.Get("MSH-12-1");
            return string.IsNullOrEmpty(version) ? "Unknown" : version;
        }
        catch
        {
            // Terser can throw an exception if the path is not found
            return "Unknown";
        }
    }

    /// <summary>
    /// Checks if the HL7 version is 2.5 or above (modified to be more lenient)
    /// </summary>
    private static bool IsVersion28OrAbove(string version)
    {
        if (string.IsNullOrEmpty(version) || version == "Unknown")
            return false;

        try
        {
            // Extract major and minor version numbers
            // Expected format: "2.8" or "2.8.1" etc.
            var parts = version.Split('.');

            if (parts.Length < 2)
                return false;

            if (!int.TryParse(parts[0], out int major) || !int.TryParse(parts[1], out int minor))
                return false;

            // Check if version is 2.5 or above (more lenient for legacy data types)
            return (major > 2) || (major == 2 && minor >= 5);
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Validates that the message has required segments
    /// </summary>
    private static bool ValidateRequiredSegments(IMessage message)
    {
        try
        {
            // MSH segment is mandatory for all HL7 messages
            var mshSegment = message.GetStructure("MSH");
            return mshSegment != null;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Generates a comprehensive insights report
    /// </summary>
    private static Task GenerateInsightsReport(List<ValidationResult> results, int totalFilesFound, ValidationConfig config, TimeSpan totalProcessingTime)
    {
        Console.WriteLine();
        Console.WriteLine("╔══════════════════════════════════════════════════════════════════════════════╗");
        Console.WriteLine("║                           VALIDATION INSIGHTS REPORT                        ║");
        Console.WriteLine("╚══════════════════════════════════════════════════════════════════════════════╝");
        Console.WriteLine();

        // Basic Statistics
        var validCount = results.Count(r => r.IsValid);
        var invalidCount = results.Count - validCount;
        var successRate = results.Count > 0 ? (double)validCount / results.Count * 100 : 0;

        Console.WriteLine("📊 PROCESSING SUMMARY");
        Console.WriteLine("─────────────────────");
        Console.WriteLine($"Total files found:      {totalFilesFound:N0}");
        Console.WriteLine($"Files processed:        {results.Count:N0}");
        if (config.MessageLimit.HasValue && totalFilesFound > config.MessageLimit.Value)
        {
            Console.WriteLine($"Files skipped (limit):   {totalFilesFound - results.Count:N0}");
        }
        Console.WriteLine($"Valid messages:         {validCount:N0}");
        Console.WriteLine($"Invalid messages:       {invalidCount:N0}");
        Console.WriteLine($"Success rate:           {successRate:F1}%");
        Console.WriteLine($"Total processing time:  {FormatTimeSpan(totalProcessingTime)}");
        Console.WriteLine();

        // Version Breakdown
        Console.WriteLine("📋 HL7 VERSION BREAKDOWN");
        Console.WriteLine("─────────────────────────");
        var versionGroups = results
            .Where(r => !string.IsNullOrEmpty(r.Version) && r.Version != "Unknown")
            .GroupBy(r => r.Version)
            .OrderBy(g => g.Key)
            .ToList();

        if (versionGroups.Any())
        {
            foreach (var group in versionGroups)
            {
                var isSupported = IsVersion28OrAbove(group.Key) ? "✓" : "✗";
                Console.WriteLine($"  {isSupported} v{group.Key}: {group.Count():N0} files");
            }
        }
        else
        {
            Console.WriteLine("  No version information available");
        }

        var unknownVersionCount = results.Count(r => string.IsNullOrEmpty(r.Version) || r.Version == "Unknown");
        if (unknownVersionCount > 0)
        {
            Console.WriteLine($"  ? Unknown version: {unknownVersionCount:N0} files");
        }
        Console.WriteLine();

        // Supported vs Unsupported Versions
        Console.WriteLine("🎯 VERSION COMPLIANCE");
        Console.WriteLine("─────────────────────");
        var supportedCount = results.Count(r => !string.IsNullOrEmpty(r.Version) && r.Version != "Unknown" && IsVersion28OrAbove(r.Version));
        var unsupportedCount = results.Count(r => !string.IsNullOrEmpty(r.Version) && r.Version != "Unknown" && !IsVersion28OrAbove(r.Version));

        Console.WriteLine($"Supported versions (≥2.8): {supportedCount:N0} files");
        Console.WriteLine($"Unsupported versions (<2.8): {unsupportedCount:N0} files");
        Console.WriteLine();

        // Error Analysis
        if (invalidCount > 0)
        {
            Console.WriteLine("❌ COMMON VALIDATION ERRORS");
            Console.WriteLine("────────────────────────────");
            var errorGroups = results
                .Where(r => !r.IsValid && !string.IsNullOrEmpty(r.ErrorMessage))
                .GroupBy(r => GetErrorCategory(r.ErrorMessage))
                .OrderByDescending(g => g.Count())
                .Take(5)
                .ToList();

            foreach (var group in errorGroups)
            {
                Console.WriteLine($"  • {group.Key}: {group.Count():N0} files");
            }
            Console.WriteLine();
        }

        // File Size Statistics
        if (results.Any(r => r.FileSizeBytes > 0))
        {
            Console.WriteLine("📁 FILE SIZE STATISTICS");
            Console.WriteLine("───────────────────────");
            var fileSizes = results.Where(r => r.FileSizeBytes > 0).Select(r => r.FileSizeBytes).ToList();

            Console.WriteLine($"Smallest file:    {FormatFileSize(fileSizes.Min())}");
            Console.WriteLine($"Largest file:     {FormatFileSize(fileSizes.Max())}");
            Console.WriteLine($"Average size:     {FormatFileSize((long)fileSizes.Average())}");
            Console.WriteLine($"Total size:       {FormatFileSize(fileSizes.Sum())}");
            Console.WriteLine();
        }

        // Performance Metrics
        if (results.Any(r => r.ProcessingTime.TotalMilliseconds > 0))
        {
            Console.WriteLine("⚡ PERFORMANCE METRICS");
            Console.WriteLine("──────────────────────");
            var processingTimes = results.Where(r => r.ProcessingTime.TotalMilliseconds > 0).Select(r => r.ProcessingTime.TotalMilliseconds).ToList();

            Console.WriteLine($"Fastest validation:   {processingTimes.Min():F1}ms");
            Console.WriteLine($"Slowest validation:   {processingTimes.Max():F1}ms");
            Console.WriteLine($"Average time:         {processingTimes.Average():F1}ms");
            Console.WriteLine($"Files per second:     {(results.Count / totalProcessingTime.TotalSeconds):F1}");
            Console.WriteLine();
        }

        // Detailed Error List (if any)
        if (invalidCount > 0)
        {
            Console.WriteLine("📝 DETAILED ERROR LIST");
            Console.WriteLine("──────────────────────");
            var invalidResults = results.Where(r => !r.IsValid).Take(10).ToList();

            foreach (var result in invalidResults)
            {
                Console.WriteLine($"  • {Path.GetFileName(result.FilePath)}");
                Console.WriteLine($"    Error: {result.ErrorMessage}");
                if (!string.IsNullOrEmpty(result.Version))
                {
                    Console.WriteLine($"    Version: {result.Version}");
                }
                Console.WriteLine();
            }

            if (invalidCount > 10)
            {
                Console.WriteLine($"  ... and {invalidCount - 10} more invalid files");
                Console.WriteLine();
            }
        }

        Console.WriteLine("╔══════════════════════════════════════════════════════════════════════════════╗");
        Console.WriteLine("║                              END OF REPORT                                  ║");
        Console.WriteLine("╚══════════════════════════════════════════════════════════════════════════════╝");
        return Task.CompletedTask;
    }

    /// <summary>
    /// Categorizes error messages for reporting
    /// </summary>
    private static string GetErrorCategory(string errorMessage)
    {
        if (string.IsNullOrEmpty(errorMessage))
            return "Unknown Error";

        var lowerError = errorMessage.ToLower();

        // New category for HL7 conformance errors (highest priority)
        if (lowerError.Contains("hl7 conformance error"))
            return "HL7 Conformance";
        if (lowerError.Contains("encoding") || lowerError.Contains("determine encoding"))
            return "Encoding Issues";
        if (lowerError.Contains("version") && (lowerError.Contains("not 2.5") || lowerError.Contains("not 2.8")))
            return "Version Compliance";
        if (lowerError.Contains("empty") || lowerError.Contains("whitespace"))
            return "Empty Files";
        if (lowerError.Contains("parse") || lowerError.Contains("parsing"))
            return "Parsing Errors";
        if (lowerError.Contains("segment") || lowerError.Contains("msh"))
            return "Missing Segments";
        if (lowerError.Contains("null"))
            return "Null Message";

        return "Other Errors";
    }

    /// <summary>
    /// Formats file size in human-readable format
    /// </summary>
    private static string FormatFileSize(long bytes)
    {
        string[] sizes = { "B", "KB", "MB", "GB" };
        double len = bytes;
        int order = 0;
        while (len >= 1024 && order < sizes.Length - 1)
        {
            order++;
            len = len / 1024;
        }
        return $"{len:0.##} {sizes[order]}";
    }

    /// <summary>
    /// Formats timespan in human-readable format
    /// </summary>
    private static string FormatTimeSpan(TimeSpan timeSpan)
    {
        if (timeSpan.TotalSeconds < 1)
            return $"{timeSpan.TotalMilliseconds:F0}ms";
        if (timeSpan.TotalMinutes < 1)
            return $"{timeSpan.TotalSeconds:F1}s";
        if (timeSpan.TotalHours < 1)
            return $"{timeSpan.Minutes}m {timeSpan.Seconds}s";
        return $"{timeSpan.Hours}h {timeSpan.Minutes}m {timeSpan.Seconds}s";
    }

    /// <summary>
    /// Parses command line arguments
    /// </summary>
    private static ValidationConfig ParseCommandLineArguments(string[] args)
    {
        var config = new ValidationConfig();

        for (int i = 0; i < args.Length; i++)
        {
            switch (args[i].ToLower())
            {
                case "--limit":
                case "-l":
                    if (i + 1 < args.Length && int.TryParse(args[i + 1], out int limit) && limit > 0)
                    {
                        config.MessageLimit = limit;
                        i++; // Skip the next argument as it's the limit value
                    }
                    else
                    {
                        Console.WriteLine("Warning: Invalid limit value. Using no limit.");
                    }
                    break;
                case "--help":
                case "-h":
                    PrintUsage();
                    Environment.Exit(0);
                    break;
            }
        }

        return config;
    }

    /// <summary>
    /// Prints usage information
    /// </summary>
    private static void PrintUsage()
    {
        Console.WriteLine("HL7 Message Validator v2.8+");
        Console.WriteLine("Usage: Hl7Validator [options]");
        Console.WriteLine();
        Console.WriteLine("Options:");
        Console.WriteLine("  -l, --limit <number>    Limit the number of messages to validate");
        Console.WriteLine("  -h, --help             Show this help message");
        Console.WriteLine();
        Console.WriteLine("Examples:");
        Console.WriteLine("  Hl7Validator                    # Validate all messages");
        Console.WriteLine("  Hl7Validator --limit 10         # Validate first 10 messages");
        Console.WriteLine("  Hl7Validator -l 5               # Validate first 5 messages");
    }
}